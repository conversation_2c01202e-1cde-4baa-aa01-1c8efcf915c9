<?php

namespace Modules\Organization\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Http\Requests\SetupDomainRequest;
use Modules\Organization\Http\Requests\StoreDomainRequest;
use Modules\Organization\Http\Requests\UpdateDomainRequest;
use Modules\Organization\Models\Domain;
use Modules\Organization\Traits\OrganizationFromRequest;
use Symfony\Component\HttpFoundation\Response;

class DomainController extends Controller
{
    use OrganizationFromRequest;

    /**
     * Get domain configuration by hostname.
     */
    public function getConfig(Request $request): Response
    {
        $hostname = $request->get('hostname', $request->getHost());
        $cacheKey = "domain_config_{$hostname}";

        $config = Cache::remember($cacheKey, 3600, function () use ($hostname) {
            return Domain::where('frontend_hostname', $hostname)->active()->first()?->config;
        });

        if (! $config) {
            return ResponseHelper::error('Không tìm thấy cấu hình domain', null, 404);
        }

        return ResponseHelper::success(data: $config);
    }

    /**
     * Get the domain for the organization (since each org has only one domain).
     */
    public function index(Request $request): Response
    {
        $organization = $this->getOrganizationFromRequest($request);

        if (! $organization) {
            return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
        }

        $domain = Domain::byOrganization($organization->id)
            ->active()
            ->first();

        if (! $domain) {
            return ResponseHelper::error('Tổ chức này chưa cấu hình domain', null, 404);
        }

        return ResponseHelper::success(data: $domain->toArray());
    }

    /**
     * Create a new domain.
     */
    public function store(StoreDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (! $organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $data = $request->validated();

            // Automatically set organization_id from the route
            $data['organization_id'] = $organization->id;

            // Check if the organization already has a domain (limit 1 per organization)
            $existingDomain = Domain::where('organization_id', $organization->id)->first();
            if ($existingDomain) {
                return ResponseHelper::error('Tổ chức chỉ có thể có một domain. Vui lòng cập nhật domain hiện tại thay vì tạo mới.', null, 400);
            }

            // Validate that at least one hostname is provided
            if (empty($data['frontend_hostname']) && empty($data['backend_hostname'])) {
                return ResponseHelper::error('Vui lòng nhập ít nhất một trong Frontend Domain hoặc Backend Domain', null, 422);
            }

            // Set default status
            $data['status'] = 'active';
            $data['is_active'] = true;

            $domain = Domain::create($data);

            return ResponseHelper::success('Tạo domain thành công', $domain, 201);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Update a domain.
     */
    public function update(UpdateDomainRequest $request, $organization, string $id): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (! $organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $domain = Domain::where('id', $id)
                ->where('organization_id', $organization->id)
                ->first();

            if (! $domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            $data = $request->validated();

            // No additional validation needed for hostname fields

            $domain->update($data);

            return ResponseHelper::success('Cập nhật domain thành công');
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            \Log::error('Domain update error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Delete a domain.
     */
    public function destroy(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (! $organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $domain = Domain::where('id', $id)
                ->where('organization_id', $organization->id)
                ->first();

            if (! $domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            $domain->delete();

            return ResponseHelper::success('Xóa domain thành công');
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomain(SetupDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (! $organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            // Validate domain format
            $errors = $this->validateDomainFormat($request->hostname);
            if (! empty($errors)) {
                return ResponseHelper::error('Xác thực domain thất bại', $errors, 422);
            }

            $domain = $this->setupDomainForOrganization(
                $request->hostname,
                $organization->id,
                $request->only(['name', 'description'])
            );

            return ResponseHelper::success('Khởi tạo thiết lập domain thành công', $domain, 201);
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Check if the hostname is available.
     */
    private function isHostnameAvailable(string $hostname, ?string $excludeId = null): bool
    {
        $query = Domain::where('hostname', $hostname);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    /**
     * Clear domain cache.
     */
    private function clearDomainCache(string $hostname): void
    {
        Cache::forget("domain_config_{$hostname}");
    }

    /**
     * Validate domain format.
     */
    private function validateDomainFormat(string $hostname): array
    {
        $errors = [];

        // Basic hostname validation
        if (! filter_var($hostname, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
            $errors[] = 'Định dạng tên miền không hợp lệ';
        }

        // Check for reserved domains
        $reservedDomains = ['localhost', 'autopay.vn', 'www.autopay.vn'];
        if (in_array($hostname, $reservedDomains)) {
            $errors[] = 'Tên miền này đã được bảo lưu và không thể sử dụng';
        }

        return $errors;
    }

    /**
     * Setup domain for organization.
     */
    private function setupDomainForOrganization(string $hostname, string $organizationId, array $additionalData = []): Domain
    {
        // Check if the organization already has a domain
        $existingDomain = Domain::where('organization_id', $organizationId)->first();
        if ($existingDomain) {
            throw new Exception('Tổ chức đã có domain được cấu hình');
        }

        // Determine domain type
        $appDomain = config('app.domain', 'autopay.vn');
        $domainType = str_ends_with($hostname, ".{$appDomain}") ? 'subdomain' : 'custom';

        // Create domain
        $domainData = array_merge([
            'organization_id' => $organizationId,
            'hostname' => $hostname,
            'name' => $additionalData['name'] ?? $hostname,
            'description' => $additionalData['description'] ?? null,
            'domain_type' => $domainType,
            'status' => $domainType === 'subdomain' ? 'active' : 'pending',
            'is_active' => true,
            'force_https' => true,
        ], $additionalData);

        return Domain::create($domainData);
    }

    /**
     * Upload file for domain branding (logo, favicon, etc.)
     */
    public function uploadFile(Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (! $organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            // Validate file upload
            $request->validate([
                'file' => [
                    'required',
                    'file',
                    'image',
                    'mimes:jpeg,jpg,png,gif,webp,svg',
                    'max:5120', // 5MB in kilobytes
                ],
            ], [
                'file.required' => 'Vui lòng chọn file để upload.',
                'file.file' => 'File upload không hợp lệ.',
                'file.image' => 'File phải là hình ảnh.',
                'file.mimes' => 'File phải có định dạng: jpeg, jpg, png, gif, webp, svg.',
                'file.max' => 'Kích thước file không được vượt quá 5MB.',
            ]);

            $file = $request->file('file');

            // Generate unique filename with organization prefix
            $filename = $organization->id . '_' . Str::uuid() . '.' . $file->getClientOriginalExtension();

            // Create image manager instance
            $manager = new ImageManager(new Driver());

            // Read and resize image
            $image = $manager->read($file->getPathname());

            // Resize image to max height of 50px while maintaining aspect ratio
            $image->scaleDown(height: 50);

            // Get the storage path
            $storagePath = storage_path('app/public/uploads/organizations/' . $filename);

            // Ensure directory exists
            $directory = dirname($storagePath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Save resized image
            $image->save($storagePath);

            // Generate public URL
            $path = 'uploads/organizations/' . $filename;
            $url = Storage::disk('public')->url($path);

            return ResponseHelper::success('File uploaded successfully', [
                'url' => $url,
                'path' => $path,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ]);

        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error('Upload thất bại: ' . $e->getMessage(), null, 500);
        }
    }
}
