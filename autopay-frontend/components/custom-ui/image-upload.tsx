'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/lib/hooks/useAuth'
import { cn } from '@/lib/utils'
import { getCurrentApiUrl } from '@/lib/utils/fetchHelper'
import Cookies from 'js-cookie'
import { Image as ImageIcon, Upload, X } from 'lucide-react'
import { useRef, useState } from 'react'
import { toast } from 'sonner'

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove: () => void
  disabled?: boolean
  className?: string
  accept?: string
  maxSize?: number // in MB
  placeholder?: string
  uploadEndpoint?: string // Custom upload endpoint, defaults to domains/upload
  // Examples:
  // - "domains/upload" (default) - for domain branding
  // - "users/avatar" - for user profile pictures
  // - "products/images" - for product images
  // - "https://external-api.com/upload" - for external services
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled = false,
  className,
  accept = 'image/*',
  maxSize = 5, // 5MB default
  placeholder = 'Chọn hình ảnh',
  uploadEndpoint = 'domains/upload', // Default to domains upload
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user } = useAuth()

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`Kích thước file không được vượt quá ${maxSize}MB`)
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Vui lòng chọn file hình ảnh')
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)

      // Check authentication
      if (!user?.current_organization?.id) {
        throw new Error('Authentication required')
      }

      // Get auth token from cookies
      const authToken = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')
      if (!authToken) {
        throw new Error('Authentication token not found')
      }

      // Upload directly to backend
      const apiUrl = getCurrentApiUrl()
      const fullUploadUrl = uploadEndpoint.startsWith('http')
        ? uploadEndpoint
        : `${apiUrl}/${user.current_organization.id}/${uploadEndpoint}`

      const response = await fetch(fullUploadUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const data = await response.json()
      onChange(data.data.url)
      toast.success('Upload thành công')
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Upload thất bại. Vui lòng thử lại.')
    } finally {
      setIsUploading(false)
      // Reset input value
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleClick = () => {
    if (disabled || isUploading) return
    fileInputRef.current?.click()
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRemove()
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div
        onClick={handleClick}
        className={cn(
          'relative cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 transition-colors hover:border-gray-400',
          disabled && 'cursor-not-allowed opacity-50',
          isUploading && 'cursor-wait opacity-50'
        )}>
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          disabled={disabled || isUploading}
          className="hidden"
        />

        {value ? (
          <div className="relative">
            <img
              src={value}
              alt="Preview"
              className="mx-auto max-h-32 rounded-md object-contain"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
              onClick={handleRemove}
              disabled={disabled || isUploading}>
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          <div className="text-center">
            <div className="mx-auto mb-2 h-12 w-12 text-gray-400">
              {isUploading ? (
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-gray-900"></div>
              ) : (
                <ImageIcon className="h-12 w-12" />
              )}
            </div>
            <div className="text-sm text-gray-600">{isUploading ? 'Đang upload...' : placeholder}</div>
            <div className="mt-1 text-xs text-gray-400">PNG, JPG, GIF tối đa {maxSize}MB</div>
          </div>
        )}
      </div>

      {value && (
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span className="truncate">{value}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleRemove}
            disabled={disabled || isUploading}>
            Xóa
          </Button>
        </div>
      )}
    </div>
  )
}
