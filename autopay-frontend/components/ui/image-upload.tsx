'use client'

import { useState, useRef } from 'react'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove: () => void
  disabled?: boolean
  className?: string
  accept?: string
  maxSize?: number // in MB
  placeholder?: string
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled = false,
  className,
  accept = 'image/*',
  maxSize = 5, // 5MB default
  placeholder = 'Chọn hình ảnh'
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`<PERSON><PERSON><PERSON> thước file không được vượt quá ${maxSize}MB`)
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Vui lòng chọn file hình ảnh')
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)

      // Upload to backend API
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const data = await response.json()
      onChange(data.url)
      toast.success('Upload thành công')
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Upload thất bại. Vui lòng thử lại.')
    } finally {
      setIsUploading(false)
      // Reset input value
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleClick = () => {
    if (disabled || isUploading) return
    fileInputRef.current?.click()
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRemove()
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div
        onClick={handleClick}
        className={cn(
          'relative border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-gray-400 transition-colors cursor-pointer',
          disabled && 'opacity-50 cursor-not-allowed',
          isUploading && 'opacity-50 cursor-wait'
        )}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          disabled={disabled || isUploading}
          className="hidden"
        />

        {value ? (
          <div className="relative">
            <img
              src={value}
              alt="Preview"
              className="max-h-32 mx-auto rounded-md object-contain"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
              onClick={handleRemove}
              disabled={disabled || isUploading}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          <div className="text-center">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-2">
              {isUploading ? (
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
              ) : (
                <ImageIcon className="h-12 w-12" />
              )}
            </div>
            <div className="text-sm text-gray-600">
              {isUploading ? 'Đang upload...' : placeholder}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              PNG, JPG, GIF tối đa {maxSize}MB
            </div>
          </div>
        )}
      </div>

      {value && (
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span className="truncate">{value}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleRemove}
            disabled={disabled || isUploading}
          >
            Xóa
          </Button>
        </div>
      )}
    </div>
  )
}
