'use client'

import { useDomain } from '@/lib/hooks/useDomain'
import { JSX, useEffect } from 'react'

/**
 * Component to apply domain-specific theme styles
 * This component should be placed high in the component tree
 */
export function DomainTheme(): JSX.Element | null {
  const { config } = useDomain()

  useEffect(() => {
    if (config?.theme?.name) {
      // Remove existing theme classes from body
      const body = document.body
      const existingThemeClasses = Array.from(body.classList).filter((className) => className.startsWith('theme-'))

      existingThemeClasses.forEach((className) => {
        body.classList.remove(className)
      })

      // Add new theme class
      body.classList.add(`theme-${config.theme.name}`)
    }

    // Cleanup when no config
    return () => {
      if (!config) {
        const body = document.body
        const existingThemeClasses = Array.from(body.classList).filter((className) => className.startsWith('theme-'))

        existingThemeClasses.forEach((className) => {
          body.classList.remove(className)
        })
      }
    }
  }, [config])

  return null
}
