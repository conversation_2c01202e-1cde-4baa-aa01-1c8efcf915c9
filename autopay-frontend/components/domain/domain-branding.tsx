'use client'

import { useDomain } from '@/lib/hooks/useDomain'
import { ChevronsUpDown } from 'lucide-react'
import React from 'react'
import { DomainLogo } from './domain-logo'

interface DomainBrandingProps {
  className?: string
  showDropdown?: boolean
  onClick?: () => void
}

export function DomainBranding({ className, showDropdown = false, onClick }: DomainBrandingProps): JSX.Element {
  const { config } = useDomain()

  const brandName = config?.branding?.name || config?.name || 'AutoPAY'
  const slogan = config?.branding?.slogan

  return (
    <button
      className={className}
      onClick={onClick}
      type="button">
      <div className="flex items-center gap-2">
        <DomainLogo
          size="md"
          showText={false}
        />

        <div className="grid flex-1 text-left leading-tight">
          <span className="truncate font-semibold">{brandName}</span>
          {slogan && <span className="text-muted-foreground truncate text-xs">{slogan}</span>}
        </div>

        {showDropdown && <ChevronsUpDown className="ml-auto size-4" />}
      </div>
    </button>
  )
}
