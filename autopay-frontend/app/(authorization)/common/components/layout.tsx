'use client'

import { useStore } from '@/app/(authorization)/common/stores/store'
import Logo from '@/assets/images/logo-black.png'
import AnimatedGridPattern from '@/components/ui/animated-grid-pattern'
import { cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { ReactNode, useEffect } from 'react'

type LayoutProps = {
  children: ReactNode
}

export default function Component({ children }: LayoutProps) {
  const pathname = usePathname()
  const { setMessage } = useStore()

  // Get domain configuration for branding
  const { data: domainConfig } = useQuery({
    queryKey: ['domain-config'],
    queryFn: async () => {
      try {
        return await queryFetchHelper('/domain-config')
      } catch (error) {
        // Return null if domain config not found (fallback to default)
        return null
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Extract branding data
  const branding = domainConfig?.data?.branding || {}
  const brandName = branding.name || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'
  const brandSlogan = branding.slogan || 'Thanh toán tự động & chia sẻ biến động số dư'
  const logoUrl = branding.logo_url
  const faviconUrl = branding.favicon_url

  useEffect(() => {
    // Reset "message" state on route change
    setMessage('')
  }, [pathname, setMessage])

  // Update favicon dynamically
  useEffect(() => {
    if (faviconUrl) {
      const link = (document.querySelector("link[rel*='icon']") as HTMLLinkElement) || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = faviconUrl
      document.getElementsByTagName('head')[0].appendChild(link)
    }
  }, [faviconUrl])

  return (
    <div className="h-screen w-full lg:grid lg:grid-cols-2">
      <div className="relative flex h-full flex-col items-center justify-center gap-4 py-12">
        <Image
          height="40"
          src={Logo}
          className="md:hidden dark:invert-100"
          alt={process.env.NEXT_PUBLIC_APP_NAME!}
        />
        <div className="mx-auto grid w-full gap-6 md:w-2/3 md:max-w-2xl md:gap-0">{children}</div>
      </div>
      <div className="relative hidden overflow-hidden bg-zinc-900 lg:block">
        <div className="bg-background z-10 flex h-full flex-col items-center justify-center text-black">
          <h2 className="dark:text-foreground text-2xl font-black tracking-widest md:text-6xl">
            <Image
              height="50"
              src={Logo}
              className="dark:invert-100"
              alt={process.env.NEXT_PUBLIC_APP_NAME!}
            />
          </h2>
          <p className="dark:text-foreground mt-6 max-w-xl text-center md:text-2xl">
            Thanh toán tự động & chia sẻ biến động số dư
          </p>

          <p className="text-muted-foreground absolute right-2 bottom-2 z-50 text-xs">xPaypment v1.0</p>
        </div>
        <AnimatedGridPattern
          numSquares={100}
          maxOpacity={0.5}
          duration={1}
          repeatDelay={10}
          className={cn(
            'h-full w-full [mask-image:radial-gradient(1500px_circle_at_center,white,transparent)]',
            'inset-x-0 inset-y-[-30%] h-[200%] skew-y-12'
          )}
        />
      </div>
    </div>
  )
}
