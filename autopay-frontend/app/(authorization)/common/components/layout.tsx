'use client'

import { useStore } from '@/app/(authorization)/common/stores/store'
import Logo from '@/assets/images/logo-black.png'
import AnimatedGridPattern from '@/components/ui/animated-grid-pattern'
import { useDomain } from '@/lib/hooks/use-domain'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { ReactNode, useEffect } from 'react'

type LayoutProps = {
  children: ReactNode
}

export default function Component({ children }: LayoutProps) {
  const pathname = usePathname()
  const { setMessage } = useStore()
  const { config } = useDomain()

  // Extract branding data from domain config
  const branding = config?.branding || {}
  const brandName = (branding as any)?.name || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'
  const brandSlogan = (branding as any)?.slogan || 'Thanh toán tự động & chia sẻ biến động số dư'
  const logoUrl = (branding as any)?.logo_url
  const faviconUrl = (branding as any)?.favicon_url

  useEffect(() => {
    // Reset "message" state on route change
    setMessage('')
  }, [pathname, setMessage])

  // Update favicon dynamically
  useEffect(() => {
    if (faviconUrl) {
      const link = (document.querySelector("link[rel*='icon']") as HTMLLinkElement) || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = faviconUrl
      document.getElementsByTagName('head')[0].appendChild(link)
    }
  }, [faviconUrl])

  return (
    <div className="h-screen w-full lg:grid lg:grid-cols-2">
      <div className="relative flex h-full flex-col items-center justify-center gap-4 py-12">
        {logoUrl ? (
          <img
            height="40"
            src={logoUrl}
            className="h-10 object-contain md:hidden"
            alt={brandName}
          />
        ) : (
          <Image
            height="40"
            src={Logo}
            className="md:hidden dark:invert-100"
            alt={brandName}
          />
        )}
        <div className="mx-auto grid w-full gap-6 md:w-2/3 md:max-w-2xl md:gap-0">{children}</div>
      </div>
      <div className="relative hidden overflow-hidden bg-zinc-900 lg:block">
        <div className="bg-background z-10 flex h-full flex-col items-center justify-center text-black">
          <h2 className="dark:text-foreground text-2xl font-black tracking-widest md:text-6xl">
            {logoUrl ? (
              <img
                height="50"
                src={logoUrl}
                className="h-12 object-contain"
                alt={brandName}
              />
            ) : (
              <Image
                height="50"
                src={Logo}
                className="dark:invert-100"
                alt={brandName}
              />
            )}
          </h2>
          <p className="dark:text-foreground mt-6 max-w-xl text-center md:text-2xl">{brandSlogan}</p>

          <p className="text-muted-foreground absolute right-2 bottom-2 z-50 text-xs">xPaypment v1.0</p>
        </div>
        <AnimatedGridPattern
          numSquares={100}
          maxOpacity={0.5}
          duration={1}
          repeatDelay={10}
          className={cn(
            'h-full w-full [mask-image:radial-gradient(1500px_circle_at_center,white,transparent)]',
            'inset-x-0 inset-y-[-30%] h-[200%] skew-y-12'
          )}
        />
      </div>
    </div>
  )
}
