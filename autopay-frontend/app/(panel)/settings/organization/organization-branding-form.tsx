'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { ImageUpload } from '@/components/custom-ui/image-upload'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

const organizationBrandingSchema = z.object({
  branding: z.object({
    name: z.string().optional(),
    slogan: z.string().optional(),
    logo_url: z.string().optional(),
    favicon_url: z.string().optional(),
  }),
})

type OrganizationBrandingValues = z.infer<typeof organizationBrandingSchema>

const defaultValues: Partial<OrganizationBrandingValues> = {
  branding: {
    name: '',
    slogan: '',
    logo_url: '',
    favicon_url: '',
  },
}

export function OrganizationBrandingForm() {
  const form = useForm<OrganizationBrandingValues>({
    resolver: zodResolver(organizationBrandingSchema),
    defaultValues,
    mode: 'onChange',
  })

  async function onSubmit(_data: OrganizationBrandingValues) {
    toast.info('Chức năng cập nhật thông tin tổ chức đang được phát triển')
    return
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="branding.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên thương hiệu</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="AutoPAY"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.slogan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slogan</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Thanh toán tự động thông minh"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="branding.logo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload logo"
                        accept="image/*"
                        maxSize={5}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.favicon_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Favicon</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload favicon"
                        accept="image/*"
                        maxSize={2}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Button
          type="submit"
          size="sm">
          Cập nhật thông tin
        </Button>
      </form>
    </Form>
  )
}
