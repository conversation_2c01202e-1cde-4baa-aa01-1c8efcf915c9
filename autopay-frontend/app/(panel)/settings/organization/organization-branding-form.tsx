'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import React from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { ImageUpload } from '@/components/custom-ui/image-upload'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

const organizationBrandingSchema = z.object({
  branding: z.object({
    name: z.string().optional(),
    slogan: z.string().optional(),
    logo_url: z.string().optional(),
    favicon_url: z.string().optional(),
  }),
})

type OrganizationBrandingValues = z.infer<typeof organizationBrandingSchema>

const defaultValues: Partial<OrganizationBrandingValues> = {
  branding: {
    name: '',
    slogan: '',
    logo_url: '',
    favicon_url: '',
  },
}

export function OrganizationBrandingForm() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Get current domain data
  const { data: domain } = useQuery({
    queryKey: ['domain', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/domains`)
    },
    enabled: !!user?.current_organization?.id,
  })

  const form = useForm<OrganizationBrandingValues>({
    resolver: zodResolver(organizationBrandingSchema),
    defaultValues,
    mode: 'onChange',
  })

  // Update form values when domain data is loaded
  React.useEffect(() => {
    if (domain?.data) {
      const brandingData = domain.data.data?.branding || {}

      form.reset({
        branding: {
          name: brandingData.name || '',
          slogan: brandingData.slogan || '',
          logo_url: brandingData.logo_url || '',
          favicon_url: brandingData.favicon_url || '',
        },
      })
    }
  }, [domain, form])

  // Update domain mutation
  const { mutate: updateDomain, isPending: isUpdating } = useMutation({
    mutationFn: async (data: OrganizationBrandingValues) => {
      if (!user?.current_organization?.id || !domain?.data?.id) {
        throw new Error('Không tìm thấy thông tin domain')
      }

      const currentData = domain.data.data || {}
      const currentBranding = currentData.branding || {}

      // Check for removed images and delete old files
      const filesToDelete = []

      // Check logo removal
      if (currentBranding.logo_url && !data.branding.logo_url) {
        filesToDelete.push({ type: 'logo', url: currentBranding.logo_url })
      }

      // Check favicon removal
      if (currentBranding.favicon_url && !data.branding.favicon_url) {
        filesToDelete.push({ type: 'favicon', url: currentBranding.favicon_url })
      }

      // Delete old files if any
      if (filesToDelete.length > 0) {
        for (const file of filesToDelete) {
          try {
            await queryFetchHelper(`/${user.current_organization.id}/domains/delete-file`, {
              method: 'POST',
              body: JSON.stringify({ url: file.url, type: file.type }),
            })
          } catch (error) {
            // Continue with update even if file deletion fails
          }
        }
      }

      const updateData = {
        data: {
          ...currentData,
          branding: {
            ...currentBranding,
            ...data.branding,
          },
        },
      }

      return queryFetchHelper(`/${user.current_organization.id}/domains/${domain.data.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      })
    },
    onSuccess: () => {
      toast.success('Cập nhật thông tin branding thành công')
      queryClient.invalidateQueries({ queryKey: ['domain', user?.current_organization?.id] })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật')
    },
  })

  async function onSubmit(data: OrganizationBrandingValues) {
    updateDomain(data)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="branding.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên thương hiệu</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="AutoPAY"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.slogan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slogan</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Thanh toán tự động thông minh"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="branding.logo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload logo (sẽ được resize tự động)"
                        accept="image/*"
                        maxSize={5}
                        uploadEndpoint="domains/upload"
                        uploadType="logo"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.favicon_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Favicon</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload favicon (sẽ được resize tự động)"
                        accept="image/*"
                        maxSize={2}
                        uploadEndpoint="domains/upload"
                        uploadType="favicon"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Button
          type="submit"
          size="sm"
          disabled={isUpdating}>
          {isUpdating ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
        </Button>
      </form>
    </Form>
  )
}
