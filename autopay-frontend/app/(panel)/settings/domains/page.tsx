'use client'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/lib/hooks/useAuth'
import { Domain } from '@/lib/types/domain'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertCircle, Globe, Pencil, Plus } from 'lucide-react'
import { useState } from 'react'
import { DomainForm } from './components/domain-form'

// Note: This is a client component, so metadata should be handled by parent layout

export default function DomainsPage() {
  const { user, loading: authLoading } = useAuth()
  const [showForm, setShowForm] = useState(false)
  const queryClient = useQueryClient()
  const [is404Error, setIs404Error] = useState(false)

  // Use React Query to fetch domain (single domain per organization)
  const {
    data: domain = null,
    isLoading,
    error,
  } = useQuery<Domain | null>({
    queryKey: ['domains', user?.current_organization?.id],
    queryFn: async (): Promise<Domain | null> => {
      // Get current organization ID from user
      const orgId = user?.current_organization?.id

      if (!orgId) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }

      try {
        const response = await queryFetchHelper(`/${orgId}/domains`)
        setIs404Error(false) // Reset 404 error state on successful fetch
        return response.data || null
      } catch (error: any) {
        // If it's a 404 error (organization doesn't have domain configured yet),
        // set 404 state and return null to allow adding domain
        if (error?.code === 404) {
          setIs404Error(true)
          return null
        }
        // Re-throw other errors
        setIs404Error(false)
        throw error
      }
    },
    enabled: !!user?.current_organization?.id && !authLoading, // Only fetch when orgId is available and auth is complete
  })

  const handleAddDomain = () => {
    setShowForm(true)
  }

  const getStatusBadge = (domain: Domain) => {
    switch (domain.status) {
      case 'active':
        return (
          <Badge
            variant="default"
            className="bg-green-500">
            Hoạt động
          </Badge>
        )
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-blue-600">
            Đang chờ
          </Badge>
        )
      case 'failed':
        return <Badge variant="destructive">Lỗi</Badge>
      case 'suspended':
        return (
          <Badge
            variant="outline"
            className="text-red-600">
            Tạm dừng
          </Badge>
        )
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  if (authLoading || isLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-48"
          descriptionWidth="w-64"
        />
        <FormSkeleton
          titleWidth="w-40"
          descriptionWidth="w-56"
        />
        <FormSkeleton
          titleWidth="w-32"
          descriptionWidth="w-48"
          buttonWidth="w-24"
        />
      </div>
    )
  }

  if (error && !is404Error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h3 className="mb-2 text-lg font-semibold">Có lỗi xảy ra</h3>
          <p className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Không thể tải danh sách domain'}
          </p>
        </div>
      </div>
    )
  }

  if (!user?.current_organization?.id) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-yellow-500" />
          <h3 className="mb-2 text-lg font-semibold">Không tìm thấy tổ chức</h3>
          <p className="text-muted-foreground">Vui lòng đăng nhập lại hoặc chọn tổ chức</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Quản lý Domain</h2>
        <p className="text-muted-foreground text-sm">
          Cấu hình domain cho tổ chức của bạn. Mỗi tổ chức chỉ được phép có một domain duy nhất.
        </p>
      </div>

      <div className="grid gap-4">
        {!domain || is404Error ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Globe className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">
                {is404Error ? 'Tổ chức chưa cấu hình domain' : 'Chưa có domain nào'}
              </h3>
              <p className="text-muted-foreground mb-4 text-center">
                {is404Error
                  ? 'Tổ chức của bạn chưa có domain được cấu hình. Hãy thêm domain để sử dụng white-label.'
                  : 'Cấu hình domain để sử dụng white-label cho tổ chức của bạn.'}
                <br />
                Mỗi tổ chức chỉ được phép có một domain duy nhất.
              </p>
              <Button onClick={handleAddDomain}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm Domain
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5" />
                  <div>
                    <CardTitle>
                      {domain.frontend_hostname && domain.backend_hostname
                        ? `${domain.frontend_hostname} / ${domain.backend_hostname}`
                        : domain.frontend_hostname || domain.backend_hostname || 'Chưa cấu hình'}
                    </CardTitle>
                    <CardDescription>{domain.data?.branding?.name || 'Domain của tổ chức'}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(domain)}
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => setShowForm(true)}>
                    Chỉnh sửa
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {domain.frontend_hostname && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Frontend:</span>
                    <span className="text-muted-foreground text-sm">{domain.frontend_hostname}</span>
                  </div>
                )}
                {domain.backend_hostname && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Backend:</span>
                    <span className="text-muted-foreground text-sm">{domain.backend_hostname}</span>
                  </div>
                )}
                {domain.data?.branding?.slogan && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Slogan:</span>
                    <span className="text-muted-foreground text-sm">{domain.data.branding.slogan}</span>
                  </div>
                )}
                <div className="text-muted-foreground flex items-center gap-4 text-sm">
                  <span>Cập nhật: {new Date(domain.updated_at).toLocaleDateString('vi-VN')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {showForm && (
        <DomainForm
          domain={domain && !is404Error ? domain : null}
          onClose={() => {
            setShowForm(false)
            setIs404Error(false) // Reset 404 error state when form is closed
            queryClient.invalidateQueries({ queryKey: ['domains'] })
          }}
        />
      )}
    </div>
  )
}
